#!/usr/bin/env python3
"""
即梦图生视频API逆向分析脚本 - 手动浏览器版本
使用mitmproxy代理来捕获网络请求，支持任何浏览器
"""

import asyncio
import json
import time
import os
import subprocess
import threading
from urllib.parse import urlparse, parse_qs

class JimengProxyAnalyzer:
    def __init__(self):
        self.requests_log = []
        self.responses_log = []
        self.api_endpoints = {}
        
    def start_mitm_proxy(self):
        """启动mitmproxy代理"""
        print("启动mitmproxy代理服务器...")
        print("代理地址: http://127.0.0.1:8080")
        print("请在浏览器中设置代理为: 127.0.0.1:8080")
        
        # 创建mitmproxy脚本
        mitm_script = '''
import json
import time
from mitmproxy import http

class JimengInterceptor:
    def __init__(self):
        self.log_file = "jimeng_requests.jsonl"
        
    def request(self, flow: http.HTTPFlow) -> None:
        """拦截请求"""
        url = flow.request.pretty_url
        
        # 只记录即梦相关的请求
        if any(keyword in url.lower() for keyword in [
            'jimeng.jianying.com', 'api', 'video', 'image', 'generate', 
            'upload', 'create', 'task', 'status', 'result', 'download'
        ]):
            request_data = {
                'type': 'request',
                'timestamp': time.time(),
                'method': flow.request.method,
                'url': url,
                'headers': dict(flow.request.headers),
                'content': flow.request.text if flow.request.text else None
            }
            
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(request_data, ensure_ascii=False) + '\\n')
            
            print(f"[REQUEST] {flow.request.method} {url}")
    
    def response(self, flow: http.HTTPFlow) -> None:
        """拦截响应"""
        url = flow.request.pretty_url
        
        # 只记录即梦相关的响应
        if any(keyword in url.lower() for keyword in [
            'jimeng.jianying.com', 'api', 'video', 'image', 'generate', 
            'upload', 'create', 'task', 'status', 'result', 'download'
        ]):
            response_data = {
                'type': 'response',
                'timestamp': time.time(),
                'url': url,
                'status': flow.response.status_code,
                'headers': dict(flow.response.headers),
                'content': flow.response.text if flow.response.text else None
            }
            
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(json.dumps(response_data, ensure_ascii=False) + '\\n')
            
            print(f"[RESPONSE] {flow.response.status_code} {url}")

addons = [JimengInterceptor()]
'''
        
        # 保存mitmproxy脚本
        with open('jimeng_interceptor.py', 'w', encoding='utf-8') as f:
            f.write(mitm_script)
        
        # 启动mitmproxy
        try:
            subprocess.run([
                'mitmdump', 
                '-s', 'jimeng_interceptor.py',
                '--listen-port', '8080',
                '--set', 'confdir=~/.mitmproxy'
            ])
        except FileNotFoundError:
            print("错误: 未找到mitmproxy")
            print("请先安装mitmproxy: pip install mitmproxy")
            return False
        
        return True
    
    def analyze_simple_method(self):
        """简单的分析方法 - 不使用代理"""
        print("=== 即梦图生视频API分析 ===")
        print()
        print("请按照以下步骤操作:")
        print("1. 打开360极速浏览器")
        print("2. 按F12打开开发者工具")
        print("3. 切换到 Network(网络) 标签页")
        print("4. 访问: https://jimeng.jianying.com/ai-tool/image-to-video")
        print("5. 登录你的账号")
        print("6. 上传一张图片")
        print("7. 点击生成视频")
        print("8. 观察Network标签页中的请求")
        print()
        print("重点关注以下类型的请求:")
        print("- 包含 'upload' 的请求 (图片上传)")
        print("- 包含 'generate' 或 'create' 的请求 (视频生成)")
        print("- 包含 'status' 或 'query' 的请求 (状态查询)")
        print("- 包含 'result' 或 'download' 的请求 (结果获取)")
        print()
        print("请将重要的API请求信息记录下来，包括:")
        print("- 请求URL")
        print("- 请求方法 (GET/POST)")
        print("- 请求头 (特别是Authorization)")
        print("- 请求参数")
        print("- 响应内容")
        print()
        
        # 打开浏览器
        try:
            import webbrowser
            webbrowser.open('https://jimeng.jianying.com/ai-tool/image-to-video')
            print("已尝试打开默认浏览器...")
        except:
            print("无法自动打开浏览器，请手动访问上述URL")
        
        input("完成操作后按回车键继续...")
        
        # 提供API模板
        self.provide_api_template()
    
    def provide_api_template(self):
        """提供API模板"""
        print("\n=== API分析模板 ===")
        print("基于常见的图生视频API模式，即梦的API可能包含以下端点:")
        print()
        
        api_template = {
            "base_url": "https://jimeng.jianying.com",
            "endpoints": {
                "upload_image": {
                    "url": "/api/v1/upload",
                    "method": "POST",
                    "description": "上传图片",
                    "headers": {
                        "Authorization": "Bearer <token>",
                        "Content-Type": "multipart/form-data"
                    },
                    "body": "图片文件"
                },
                "generate_video": {
                    "url": "/api/v1/generate",
                    "method": "POST", 
                    "description": "生成视频",
                    "headers": {
                        "Authorization": "Bearer <token>",
                        "Content-Type": "application/json"
                    },
                    "body": {
                        "image_id": "上传后的图片ID",
                        "duration": 5,
                        "style": "default"
                    }
                },
                "check_status": {
                    "url": "/api/v1/status",
                    "method": "GET",
                    "description": "检查生成状态",
                    "params": {
                        "task_id": "任务ID"
                    }
                },
                "get_result": {
                    "url": "/api/v1/result", 
                    "method": "GET",
                    "description": "获取生成结果",
                    "params": {
                        "task_id": "任务ID"
                    }
                }
            }
        }
        
        print(json.dumps(api_template, indent=2, ensure_ascii=False))
        
        # 保存模板
        with open('api_template.json', 'w', encoding='utf-8') as f:
            json.dump(api_template, f, indent=2, ensure_ascii=False)
        
        print(f"\nAPI模板已保存到: api_template.json")
        print("请根据实际观察到的API调用来更新这个模板")

def main():
    analyzer = JimengProxyAnalyzer()
    
    print("即梦图生视频API逆向分析工具")
    print("选择分析方法:")
    print("1. 使用mitmproxy代理 (需要安装mitmproxy)")
    print("2. 手动分析 (推荐)")
    
    choice = input("请选择 (1/2): ").strip()
    
    if choice == "1":
        # 检查是否安装了mitmproxy
        try:
            subprocess.run(['mitmdump', '--version'], 
                         capture_output=True, check=True)
            print("检测到mitmproxy，启动代理模式...")
            analyzer.start_mitm_proxy()
        except (FileNotFoundError, subprocess.CalledProcessError):
            print("未检测到mitmproxy，切换到手动分析模式...")
            analyzer.analyze_simple_method()
    else:
        analyzer.analyze_simple_method()

if __name__ == "__main__":
    main()
