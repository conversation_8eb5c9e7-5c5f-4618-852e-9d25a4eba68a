#!/usr/bin/env python3
"""
即梦图生视频API客户端
基于逆向分析结果实现的Python API客户端
"""

import requests
import time
import json
import os
import uuid
import hashlib
from typing import Optional, Dict, Any
from urllib.parse import urljoin

class JimengAPI:
    """即梦图生视频API客户端"""

    def __init__(self, session_cookies: Optional[Dict[str, str]] = None):
        """
        初始化API客户端

        Args:
            session_cookies: 浏览器会话cookies，用于身份验证
        """
        self.base_url = "https://jimeng.jianying.com"
        self.session = requests.Session()

        # 基础参数（从抓包结果获取）
        self.aid = "513695"
        self.web_version = "6.6.0"
        self.da_version = "3.2.8"
        self.aigc_features = "app_lip_sync"

        # 设置请求头（基于抓包结果）
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Content-Type': 'application/json',
            'Referer': 'https://jimeng.jianying.com/ai-tool/generate?type=video',
            'Origin': 'https://jimeng.jianying.com',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'expect-real-status': '1',
        })

        # 设置cookies
        if session_cookies:
            self.session.cookies.update(session_cookies)

        # API端点（基于抓包结果）
        self.endpoints = {
            'generate': '/mweb/v1/aigc_draft/generate',
            'status': '/mweb/v1/task/status',  # 需要进一步确认
            'result': '/mweb/v1/task/result',  # 需要进一步确认
        }

    def _generate_sign(self, timestamp: str, data: str = "") -> str:
        """
        生成请求签名
        注意：这是一个占位符实现，实际的签名算法需要进一步逆向分析

        Args:
            timestamp: 时间戳
            data: 请求数据

        Returns:
            签名字符串
        """
        # 从抓包结果看，签名是一个32位的MD5哈希值
        # 实际的签名算法需要进一步分析JavaScript代码
        # 这里使用一个简单的占位符实现
        sign_data = f"{timestamp}{data}{self.aid}"
        return hashlib.md5(sign_data.encode()).hexdigest()

    def upload_image(self, image_path: str) -> str:
        """
        上传图片
        注意：这个方法需要根据实际的图片上传接口实现

        Args:
            image_path: 图片文件路径

        Returns:
            图片URI
        """
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"图片文件不存在: {image_path}")

        # TODO: 实现图片上传逻辑
        # 根据抓包结果，图片URI格式类似：tos-cn-i-tb4s082cfz/931403230757426889b86ce55aed06d0
        # 需要找到实际的图片上传接口
        raise NotImplementedError("图片上传功能待实现，需要找到实际的上传接口")

    def generate_video(self,
                      first_frame_uri: str,
                      end_frame_uri: Optional[str] = None,
                      prompt: str = "固定镜头，完美过渡效果，电影感调色，炫酷过渡",
                      duration_ms: int = 10000,
                      fps: int = 24,
                      resolution: str = "720p",
                      video_mode: int = 2) -> str:
        """
        生成视频

        Args:
            first_frame_uri: 首帧图片URI
            end_frame_uri: 末帧图片URI（可选）
            prompt: 生成提示词
            duration_ms: 视频时长（毫秒）
            fps: 帧率
            resolution: 分辨率
            video_mode: 视频模式（2表示图片到图片）

        Returns:
            任务ID (submit_id)
        """
        # 生成唯一的任务ID
        submit_id = str(uuid.uuid4())

        # 当前时间戳
        current_time = int(time.time())
        device_time = str(current_time)

        # 构建请求URL
        url = f"{self.base_url}{self.endpoints['generate']}"
        params = {
            "aid": self.aid,
            "device_platform": "web",
            "region": "cn",
            "webId": "7533144395753244196",  # 这个可能需要动态获取
            "da_version": self.da_version,
            "web_version": self.web_version,
            "aigc_features": self.aigc_features,
        }

        # 构建draft_content（基于抓包结果的复杂结构）
        draft_content = {
            "type": "draft",
            "id": str(uuid.uuid4()),
            "min_version": "3.0.5",
            "min_features": [],
            "is_from_tsn": True,
            "version": "3.2.8",
            "main_component_id": str(uuid.uuid4()),
            "component_list": [{
                "type": "video_base_component",
                "id": str(uuid.uuid4()),
                "min_version": "1.0.0",
                "aigc_mode": "workbench",
                "metadata": {
                    "type": "",
                    "id": str(uuid.uuid4()),
                    "created_platform": 3,
                    "created_platform_version": "",
                    "created_time_in_ms": str(current_time * 1000),
                    "created_did": ""
                },
                "generate_type": "gen_video",
                "abilities": {
                    "type": "",
                    "id": str(uuid.uuid4()),
                    "gen_video": {
                        "type": "",
                        "id": str(uuid.uuid4()),
                        "text_to_video_params": {
                            "type": "",
                            "id": str(uuid.uuid4()),
                            "video_gen_inputs": [{
                                "type": "",
                                "id": str(uuid.uuid4()),
                                "min_version": "3.0.5",
                                "prompt": prompt,
                                "first_frame_image": {
                                    "type": "image",
                                    "id": str(uuid.uuid4()),
                                    "source_from": "upload",
                                    "platform_type": 1,
                                    "name": "",
                                    "image_uri": first_frame_uri,
                                    "width": 1536,  # 可能需要根据实际图片尺寸调整
                                    "height": 832,
                                    "format": "",
                                    "uri": first_frame_uri
                                },
                                "ending_control": "1.0",
                                "video_mode": video_mode,
                                "fps": fps,
                                "duration_ms": duration_ms,
                                "resolution": resolution
                            }],
                            "video_aspect_ratio": "16:9",
                            "seed": 2780884837,  # 可能需要随机生成
                            "model_req_key": "dreamina_ic_generate_video_model_vgfm_3.0",
                            "priority": 0
                        },
                        "video_task_extra": json.dumps({
                            "promptSource": "custom",
                            "isDefaultSeed": 1,
                            "originSubmitId": submit_id,
                            "isRegenerate": False,
                            "enterFrom": "click"
                        })
                    }
                },
                "process_type": 1
            }]
        }

        # 如果有结束帧，添加到配置中
        if end_frame_uri:
            draft_content["component_list"][0]["abilities"]["gen_video"]["text_to_video_params"]["video_gen_inputs"][0]["end_frame_image"] = {
                "type": "image",
                "id": str(uuid.uuid4()),
                "source_from": "upload",
                "platform_type": 1,
                "name": "",
                "image_uri": end_frame_uri,
                "width": 1536,
                "height": 832,
                "format": "",
                "uri": end_frame_uri
            }

        # 构建请求体（基于抓包结果）
        request_body = {
            "extend": {
                "root_model": "dreamina_ic_generate_video_model_vgfm_3.0",
                "m_video_commerce_info": {
                    "benefit_type": "basic_video_operation_vgfm_v_three",
                    "resource_id": "generate_video",
                    "resource_id_type": "str",
                    "resource_sub_type": "aigc"
                },
                "m_video_commerce_info_list": [{
                    "benefit_type": "basic_video_operation_vgfm_v_three",
                    "resource_id": "generate_video",
                    "resource_id_type": "str",
                    "resource_sub_type": "aigc"
                }]
            },
            "submit_id": submit_id,
            "metrics_extra": json.dumps({
                "promptSource": "custom",
                "isDefaultSeed": 1,
                "originSubmitId": submit_id,
                "isRegenerate": False,
                "enterFrom": "click"
            }),
            "draft_content": json.dumps(draft_content),
            "http_common_info": {
                "aid": int(self.aid)
            }
        }

        # 生成签名
        sign = self._generate_sign(device_time, json.dumps(request_body))

        # 设置请求头（基于抓包结果）
        headers = {
            "sign": sign,
            "sign-ver": "1",
            "device-time": device_time,
            "appid": self.aid,
            "pf": "7",
            "appvr": "5.8.0",
            "loc": "cn",
            "lan": "zh-Hans",
            "app-sdk-version": "48.0.0"
        }

        # 发送请求
        try:
            response = self.session.post(url, params=params, json=request_body, headers=headers)
            response.raise_for_status()
            result = response.json()

            # 检查响应
            if result.get("ret") == "0":
                print(f"视频生成任务提交成功，任务ID: {submit_id}")
                return submit_id
            else:
                raise Exception(f"视频生成失败: {result.get('errmsg', '未知错误')}")

        except requests.exceptions.RequestException as e:
            raise Exception(f"请求失败: {e}")
        except json.JSONDecodeError as e:
            raise Exception(f"响应解析失败: {e}")

    def check_status(self, submit_id: str) -> Dict[str, Any]:
        """
        检查任务状态
        注意：这个方法需要根据实际的状态查询接口实现

        Args:
            submit_id: 任务ID

        Returns:
            任务状态信息
        """
        # TODO: 实现状态查询逻辑
        # 需要找到实际的状态查询接口
        # 可能的接口路径：/mweb/v1/task/status 或类似
        raise NotImplementedError("状态查询功能待实现，需要找到实际的状态查询接口")

    def get_result(self, submit_id: str) -> Optional[str]:
        """
        获取生成结果

        Args:
            submit_id: 任务ID

        Returns:
            视频URL（如果完成）或None
        """
        status = self.check_status(submit_id)

        # TODO: 根据实际的状态结构解析结果
        if status.get("status") == "completed":
            return status.get("video_url")

        return None
    
    def generate_video_complete(self, image_path: str, timeout: int = 300, **kwargs) -> Dict[str, Any]:
        """
        完整的图生视频流程
        
        Args:
            image_path: 图片文件路径
            timeout: 超时时间（秒）
            **kwargs: 生成参数
            
        Returns:
            最终的视频生成结果
        """
        print(f"开始上传图片: {image_path}")
        upload_result = self.upload_image(image_path)
        image_id = upload_result.get('image_id')  # 根据实际API响应调整
        
        print(f"开始生成视频，图片ID: {image_id}")
        generate_result = self.generate_video(image_id, **kwargs)
        task_id = generate_result.get('task_id')  # 根据实际API响应调整
        
        print(f"等待生成完成，任务ID: {task_id}")
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            status_result = self.check_status(task_id)
            status = status_result.get('status')  # 根据实际API响应调整
            
            print(f"当前状态: {status}")
            
            if status == 'completed':  # 根据实际API响应调整
                print("生成完成，获取结果...")
                return self.get_result(task_id)
            elif status == 'failed':  # 根据实际API响应调整
                raise Exception("视频生成失败")
            
            time.sleep(5)  # 等待5秒后再次检查
        
        raise TimeoutError("视频生成超时")
    
    def download_video(self, video_url: str, save_path: str):
        """
        下载生成的视频
        
        Args:
            video_url: 视频URL
            save_path: 保存路径
        """
        response = self.session.get(video_url, stream=True)
        
        if response.status_code == 200:
            with open(save_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
            print(f"视频已保存到: {save_path}")
        else:
            raise Exception(f"视频下载失败: {response.status_code}")

def main():
    """示例用法"""
    # 创建API客户端
    api = JimengAPI()
    
    # 设置认证信息（需要根据逆向分析结果获取）
    # api.set_auth(token="your_token", device_id="your_device_id")
    
    try:
        # 生成视频
        result = api.generate_video_complete(
            image_path="test_image.jpg",
            duration=5,  # 示例参数
            style="default"  # 示例参数
        )
        
        # 下载视频
        video_url = result.get('video_url')
        if video_url:
            api.download_video(video_url, "generated_video.mp4")
        
        print("视频生成完成！")
        
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    main()
