#!/usr/bin/env python3
"""
即梦图生视频API逆向分析脚本
使用Playwright监听网络请求，分析API调用
"""

import asyncio
import json
import time
import os
from playwright.async_api import async_playwright
from urllib.parse import urlparse, parse_qs

class JimengAnalyzer:
    def __init__(self):
        self.requests_log = []
        self.responses_log = []
        self.api_endpoints = {}

    async def analyze_website(self):
        """分析即梦网站的图生视频功能"""
        async with async_playwright() as p:
            # 启动浏览器，使用更多反检测参数
            browser = await p.chromium.launch(
                headless=False,
                slow_mo=500,
                args=[
                    '--disable-blink-features=AutomationControlled',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--no-first-run',
                    '--no-default-browser-check',
                    '--disable-dev-shm-usage',
                    '--disable-extensions'
                ]
            )

            # 创建上下文，模拟真实用户
            context = await browser.new_context(
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                viewport={'width': 1920, 'height': 1080},
                locale='zh-CN',
                timezone_id='Asia/Shanghai'
            )

            # 添加一些反检测脚本
            await context.add_init_script("""
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });

                Object.defineProperty(navigator, 'plugins', {
                    get: () => [1, 2, 3, 4, 5],
                });

                Object.defineProperty(navigator, 'languages', {
                    get: () => ['zh-CN', 'zh', 'en'],
                });

                window.chrome = {
                    runtime: {},
                };
            """)

            page = await context.new_page()

            # 监听网络请求
            page.on("request", self.log_request)
            page.on("response", self.log_response)

            try:
                print("正在访问即梦网站...")
                # 先访问首页
                await page.goto("https://jimeng.jianying.com/",
                               wait_until="networkidle", timeout=30000)

                print("首页加载完成，等待3秒...")
                await asyncio.sleep(3)

                # 尝试直接访问图生视频页面
                print("正在访问图生视频页面...")
                await page.goto("https://jimeng.jianying.com/ai-tool/image-to-video",
                               wait_until="networkidle", timeout=30000)

                print("页面加载完成")
                print("请按照以下步骤操作:")
                print("1. 如果出现登录弹窗或需要登录，请完成登录")
                print("2. 上传一张图片")
                print("3. 点击生成视频按钮")
                print("4. 等待生成完成（或至少等到开始生成）")
                print("5. 完成后按回车键继续分析...")
                print("\n提示：如果登录有问题，可以尝试:")
                print("- 刷新页面")
                print("- 手动在地址栏输入: https://jimeng.jianying.com/ai-tool/image-to-video")
                print("- 或者先在新标签页登录，再回到这个页面")

                # 等待用户操作
                input("按回车键继续分析...")

                # 等待一段时间让所有请求完成
                await asyncio.sleep(3)

                # 分析收集到的请求
                self.analyze_requests()

            except Exception as e:
                print(f"分析过程中出现错误: {e}")
            finally:
                await browser.close()
    
    async def log_request(self, request):
        """记录请求"""
        url = request.url
        method = request.method
        headers = await request.all_headers()

        # 记录所有jimeng相关的API请求
        if any(keyword in url.lower() for keyword in [
            'jimeng.jianying.com', 'api', 'video', 'image', 'generate',
            'upload', 'create', 'task', 'status', 'result', 'download'
        ]):
            request_data = {
                'timestamp': time.time(),
                'method': method,
                'url': url,
                'headers': headers,
                'post_data': request.post_data if request.post_data else None,
                'resource_type': request.resource_type
            }
            self.requests_log.append(request_data)
            print(f"[REQUEST] {method} {url}")

            # 特别标记重要的API
            if any(key in url.lower() for key in ['generate', 'create', 'upload']):
                print(f"  ⭐ 重要API: {url}")
    
    async def log_response(self, response):
        """记录响应"""
        url = response.url
        status = response.status
        headers = response.headers

        # 记录所有jimeng相关的API响应
        if any(keyword in url.lower() for keyword in [
            'jimeng.jianying.com', 'api', 'video', 'image', 'generate',
            'upload', 'create', 'task', 'status', 'result', 'download'
        ]):
            try:
                # 尝试获取响应内容
                content_type = headers.get('content-type', '').lower()
                if 'application/json' in content_type or 'text/' in content_type:
                    content = await response.text()
                else:
                    content = f"Binary content ({content_type})"

                response_data = {
                    'timestamp': time.time(),
                    'url': url,
                    'status': status,
                    'headers': dict(headers),
                    'content': content,
                    'content_type': content_type
                }
                self.responses_log.append(response_data)
                print(f"[RESPONSE] {status} {url}")

                # 特别标记重要的API响应
                if any(key in url.lower() for key in ['generate', 'create', 'upload']):
                    print(f"  ⭐ 重要响应: {status} {url}")

            except Exception as e:
                print(f"无法获取响应内容: {e}")
    
    def analyze_requests(self):
        """分析收集到的请求和响应"""
        print("\n" + "="*50)
        print("API分析结果")
        print("="*50)
        
        # 保存原始数据
        with open('requests_log.json', 'w', encoding='utf-8') as f:
            json.dump(self.requests_log, f, ensure_ascii=False, indent=2)
        
        with open('responses_log.json', 'w', encoding='utf-8') as f:
            json.dump(self.responses_log, f, ensure_ascii=False, indent=2)
        
        print(f"共捕获到 {len(self.requests_log)} 个请求")
        print(f"共捕获到 {len(self.responses_log)} 个响应")
        
        # 分析关键API
        self.analyze_key_apis()
    
    def analyze_key_apis(self):
        """分析关键API"""
        print("\n关键API分析:")

        # 分类API
        upload_apis = []
        generate_apis = []
        status_apis = []
        result_apis = []
        other_apis = []

        for req in self.requests_log:
            url = req['url']
            method = req['method']

            # 分析不同类型的API
            if 'upload' in url.lower():
                upload_apis.append(req)
            elif 'generate' in url.lower() or 'create' in url.lower():
                generate_apis.append(req)
            elif 'status' in url.lower() or 'progress' in url.lower() or 'query' in url.lower():
                status_apis.append(req)
            elif 'result' in url.lower() or 'download' in url.lower():
                result_apis.append(req)
            else:
                other_apis.append(req)

        # 打印分类结果
        self.print_api_category("上传API", upload_apis)
        self.print_api_category("生成API", generate_apis)
        self.print_api_category("状态查询API", status_apis)
        self.print_api_category("结果获取API", result_apis)
        self.print_api_category("其他API", other_apis)

        # 生成API总结
        self.generate_api_summary()

    def print_api_category(self, category_name, apis):
        """打印API分类"""
        if not apis:
            return

        print(f"\n{'='*20} {category_name} {'='*20}")
        for req in apis:
            print(f"\n[{req['method']}] {req['url']}")
            self.print_request_details(req)

    def generate_api_summary(self):
        """生成API总结"""
        print(f"\n{'='*50}")
        print("API总结")
        print(f"{'='*50}")

        # 统计API数量
        total_requests = len(self.requests_log)
        unique_urls = len(set(req['url'] for req in self.requests_log))

        print(f"总请求数: {total_requests}")
        print(f"唯一URL数: {unique_urls}")

        # 按域名分组
        domains = {}
        for req in self.requests_log:
            domain = urlparse(req['url']).netloc
            if domain not in domains:
                domains[domain] = []
            domains[domain].append(req)

        print(f"\n域名分布:")
        for domain, reqs in domains.items():
            print(f"  {domain}: {len(reqs)} 个请求")
    
    def print_request_details(self, req):
        """打印请求详情"""
        print(f"  方法: {req['method']}")
        print(f"  URL: {req['url']}")
        
        # 打印重要的请求头
        important_headers = ['authorization', 'content-type', 'user-agent', 'referer', 'x-', 'cookie']
        for header_name, header_value in req['headers'].items():
            if any(important in header_name.lower() for important in important_headers):
                print(f"  {header_name}: {header_value}")
        
        # 打印POST数据
        if req['post_data']:
            print(f"  POST数据: {req['post_data'][:500]}...")

async def main():
    analyzer = JimengAnalyzer()
    await analyzer.analyze_website()

if __name__ == "__main__":
    asyncio.run(main())
